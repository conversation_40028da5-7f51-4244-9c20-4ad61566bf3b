******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Sat Aug  2 13:30:12 2025

OUTPUT FILE NAME:   <TI_CAR1.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00006fc9


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00008ab8  00017548  R  X
  SRAM                  20200000   00008000  00000702  000078fe  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00008ab8   00008ab8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00007b00   00007b00    r-x .text
  00007bc0    00007bc0    00000e80   00000e80    r-- .rodata
  00008a40    00008a40    00000078   00000078    r-- .cinit
20200000    20200000    00000503   00000000    rw-
  20200000    20200000    000003cf   00000000    rw- .bss
  202003d0    202003d0    00000133   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00007b00     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  00001364    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  0000159c    0000022c     MPU6050.o (.text.Read_Quad)
                  000017c8    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  000019f4    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001c14    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00001e08    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00001fe4    000001c8     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000021ac    000001b0     Task.o (.text.Task_Start)
                  0000235c    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  000024fc    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000268e    00000002     Interrupt.o (.text.GROUP1_IRQHandler)
                  00002690    00000188     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init)
                  00002818    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  000029a0    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  00002b18    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00002c88    00000144     MPU6050.o (.text.MPU6050_Init)
                  00002dcc    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00002f08    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  0000303c    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00003170    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  000032a0    00000128     Task_App.o (.text.Task_Tracker)
                  000033c8    00000128     inv_mpu.o (.text.mpu_init)
                  000034f0    00000124     PID_IQMath.o (.text.PID_IQ_Prosc)
                  00003614    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  00003738    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00003858    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00003964    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00003a6c    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00003b70    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00003c70    000000f0     Motor.o (.text.Motor_SetDirc)
                  00003d60    000000f0     Task_App.o (.text.Task_Motor_PID)
                  00003e50    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  00003f3c    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00004020    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  00004104    000000e0     Task_App.o (.text.Task_Init)
                  000041e4    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  000042c0    000000dc     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Analog_value)
                  0000439c    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00004474    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  0000454c    000000d4     inv_mpu.o (.text.set_int_enable)
                  00004620    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  000046f0    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  000047b4    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  00004878    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  00004934    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  000049ec    000000b4     Task.o (.text.Task_Add)
                  00004aa0    000000ac     Task_App.o (.text.Task_Serial)
                  00004b4c    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  00004bf8    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  00004ca4    000000aa     No_Mcu_Ganv_Grayscale_Sensor.o (.text.normalizeAnalogValues)
                  00004d4e    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00004d50    000000a2                            : udivmoddi4.S.obj (.text)
                  00004df2    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00004df4    000000a0     Motor.o (.text.Motor_SetDuty)
                  00004e94    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  00004f30    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  00004fc8    00000096     MPU6050.o (.text.inv_row_2_scale)
                  0000505e    00000002     --HOLE-- [fill = 0]
                  00005060    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_Motor_PWM_init)
                  000050ec    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00005178    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00005204    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  00005290    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00005314    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00005398    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  0000541a    00000002     --HOLE-- [fill = 0]
                  0000541c    00000080     Motor.o (.text.Motor_GetSpeed)
                  0000549c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00005518    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  0000558c    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00005590    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00005604    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  00005678    00000072     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  000056ea    00000002     --HOLE-- [fill = 0]
                  000056ec    00000070     Motor.o (.text.Motor_Start)
                  0000575c    00000070     Serial.o (.text.MyPrintf_DMA)
                  000057cc    0000006c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.convertAnalogToDigital)
                  00005838    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000058a0    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00005906    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  0000596c    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  000059d0    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00005a32    00000002     --HOLE-- [fill = 0]
                  00005a34    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00005a96    00000002     --HOLE-- [fill = 0]
                  00005a98    00000060     Key_Led.o (.text.Key_Read)
                  00005af8    00000060     Task_App.o (.text.Task_IdleFunction)
                  00005b58    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  00005bb8    00000060     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00005c18    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  00005c78    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00005cd6    00000002     --HOLE-- [fill = 0]
                  00005cd8    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00005d34    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00005d90    0000005c     MPU6050.o (.text.mspm0_i2c_enable)
                  00005dec    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  00005e44    00000058     Serial.o (.text.Serial_Init)
                  00005e9c    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  00005ef4    00000058            : _printfi.c.obj (.text._pconv_f)
                  00005f4c    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00005fa2    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00005ff4    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  00006044    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  00006094    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  000060e0    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  0000612c    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00006178    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  000061c4    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  0000620e    00000002     --HOLE-- [fill = 0]
                  00006210    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  0000625a    00000002     --HOLE-- [fill = 0]
                  0000625c    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000062a4    00000048     ADC.o (.text.adc_getValue)
                  000062ec    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  00006334    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  0000637c    00000048     MPU6050.o (.text.mspm0_i2c_disable)
                  000063c4    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00006408    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  0000644c    00000044     Task_App.o (.text.Task_Key)
                  00006490    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  000064d4    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  00006518    00000042     No_Mcu_Ganv_Grayscale_Sensor.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  0000655a    00000002     --HOLE-- [fill = 0]
                  0000655c    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  0000659e    00000002     --HOLE-- [fill = 0]
                  000065a0    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  000065e0    00000040     Interrupt.o (.text.Interrupt_Init)
                  00006620    00000040     Task_App.o (.text.Task_GraySensor)
                  00006660    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  000066a0    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000066e0    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00006720    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  00006760    0000003e     Task.o (.text.Task_CMP)
                  0000679e    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  000067dc    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006818    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006854    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00006890    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000068cc    0000003c     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Anolog_Value)
                  00006908    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00006944    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00006980    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000069bc    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  000069f6    00000002     --HOLE-- [fill = 0]
                  000069f8    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00006a32    00000038     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Normalize_For_User)
                  00006a6a    00000002     --HOLE-- [fill = 0]
                  00006a6c    00000038     Task_App.o (.text.Task_LED)
                  00006aa4    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00006adc    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006b10    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006b44    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  00006b78    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  00006baa    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  00006bdc    00000030     ADC.o (.text.DL_ADC12_getMemResult)
                  00006c0c    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00006c3c    00000030     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutputFeatures)
                  00006c6c    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00006c9c    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00006ccc    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00006cfc    00000030            : vsnprintf.c.obj (.text._outs)
                  00006d2c    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  00006d5c    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  00006d8c    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  00006db8    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00006de4    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  00006e0e    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  00006e36    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00006e5e    00000002     --HOLE-- [fill = 0]
                  00006e60    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  00006e88    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  00006eb0    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00006ed8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00006f00    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00006f28    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00006f50    00000028     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00006f78    00000028     SysTick.o (.text.SysTick_Increasment)
                  00006fa0    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00006fc8    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00006ff0    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  00007016    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  0000703c    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00007062    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00007088    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  000070ac    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  000070d0    00000024                            : muldi3.S.obj (.text.__muldi3)
                  000070f4    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00007116    00000002     --HOLE-- [fill = 0]
                  00007118    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00007138    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00007158    00000020     SysTick.o (.text.Delay)
                  00007178    00000020     main.o (.text.main)
                  00007198    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  000071b8    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  000071d6    00000002     --HOLE-- [fill = 0]
                  000071d8    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  000071f6    00000002     --HOLE-- [fill = 0]
                  000071f8    0000001c     ADC.o (.text.DL_ADC12_startConversion)
                  00007214    0000001c     ADC.o (.text.DL_ADC12_stopConversion)
                  00007230    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  0000724c    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  00007268    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00007284    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  000072a0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  000072bc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  000072d8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000072f4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00007310    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  0000732c    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  00007348    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00007364    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00007380    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  0000739c    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000073b8    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000073d4    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  000073f0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00007408    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00007420    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  00007438    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  00007450    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00007468    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00007480    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  00007498    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000074b0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  000074c8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000074e0    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  000074f8    00000018     Motor.o (.text.DL_GPIO_setPins)
                  00007510    00000018     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_setPins)
                  00007528    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00007540    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00007558    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00007570    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  00007588    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000075a0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  000075b8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  000075d0    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  000075e8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00007600    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  00007618    00000018     MPU6050.o (.text.DL_I2C_reset)
                  00007630    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00007648    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00007660    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00007678    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00007690    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000076a8    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000076c0    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000076d8    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000076f0    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00007708    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  00007720    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00007738    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00007750    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00007768    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00007780    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00007798    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  000077b0    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  000077c8    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  000077e0    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  000077f8    00000016     ADC.o (.text.DL_ADC12_disableConversions)
                  0000780e    00000016     ADC.o (.text.DL_ADC12_enableConversions)
                  00007824    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  0000783a    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  00007850    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  00007866    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  0000787c    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00007892    00000016     SysTick.o (.text.SysGetTick)
                  000078a8    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  000078be    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  000078d2    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  000078e6    00000014     No_Mcu_Ganv_Grayscale_Sensor.o (.text.DL_GPIO_clearPins)
                  000078fa    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  0000790e    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00007922    00000002     --HOLE-- [fill = 0]
                  00007924    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  00007938    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  0000794c    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00007960    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00007974    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00007988    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  0000799c    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000079b0    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  000079c4    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  000079d8    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000079ec    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000079fe    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00007a10    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00007a22    00000010     ADC.o (.text.DL_ADC12_getStatus)
                  00007a32    00000002     --HOLE-- [fill = 0]
                  00007a34    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00007a44    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00007a54    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00007a64    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00007a74    0000000e     No_Mcu_Ganv_Grayscale_Sensor.o (.text.Get_Digtal_For_User)
                  00007a82    00000002     --HOLE-- [fill = 0]
                  00007a84    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00007a92    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00007aa0    0000000e     MPU6050.o (.text.tap_cb)
                  00007aae    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00007abc    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00007ac8    0000000c     SysTick.o (.text.Sys_GetTick)
                  00007ad4    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00007ade    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007ae8    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00007af8    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007b02    00000002     --HOLE-- [fill = 0]
                  00007b04    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00007b14    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007b1e    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007b28    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007b32    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00007b3c    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00007b4c    0000000a     MPU6050.o (.text.android_orient_cb)
                  00007b56    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00007b5e    00000008     Interrupt.o (.text.SysTick_Handler)
                  00007b66    00000002     --HOLE-- [fill = 0]
                  00007b68    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00007b70    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00007b78    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007b7e    00000002     --HOLE-- [fill = 0]
                  00007b80    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00007b90    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00007b96    00000006            : exit.c.obj (.text:abort)
                  00007b9c    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00007ba0    00000004     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00007ba4    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00007ba8    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00007bb8    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00007bbc    00000004     --HOLE-- [fill = 0]

.cinit     0    00008a40    00000078     
                  00008a40    00000050     (.cinit..data.load) [load image, compression = lzss]
                  00008a90    0000000c     (__TI_handler_table)
                  00008a9c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00008aa4    00000010     (__TI_cinit_table)
                  00008ab4    00000004     --HOLE-- [fill = 0]

.rodata    0    00007bc0    00000e80     
                  00007bc0    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  000087b6    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  000087c0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  000088c1    00000007     Task_App.o (.rodata.str1.8896853068034818020.1)
                  000088c8    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00008908    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00008930    00000028     inv_mpu.o (.rodata.test)
                  00008958    0000001f     Task_App.o (.rodata.str1.4769078833470683459.1)
                  00008977    0000001e     inv_mpu.o (.rodata.reg)
                  00008995    00000003     ti_msp_dl_config.o (.rodata.gMotor_PWMClockConfig)
                  00008998    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  000089b0    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  000089c8    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  000089d9    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  000089ea    00000011     Task_App.o (.rodata.str1.3850258909703972507.1)
                  000089fb    00000001     --HOLE-- [fill = 0]
                  000089fc    0000000c     inv_mpu.o (.rodata.hw)
                  00008a08    0000000b     Task_App.o (.rodata.str1.11683036942922059812.1)
                  00008a13    00000001     --HOLE-- [fill = 0]
                  00008a14    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00008a1c    00000008     ti_msp_dl_config.o (.rodata.gMotor_PWMConfig)
                  00008a24    00000008     Task_App.o (.rodata.str1.12629676409056169537.1)
                  00008a2c    00000006     Task_App.o (.rodata.str1.3743034515018940988.1)
                  00008a32    00000004     Task_App.o (.rodata.str1.10635198597896025474.1)
                  00008a36    00000004     Task_App.o (.rodata.str1.16020955549137178199.1)
                  00008a3a    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00008a3c    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  00008a3e    00000002     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000003cf     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    000000b0     (.common:GraySensor)
                  202003a0    00000010     (.common:quat)
                  202003b0    00000006     (.common:Data_Accel)
                  202003b6    00000006     (.common:Data_Gyro)
                  202003bc    00000004     (.common:Data_Pitch)
                  202003c0    00000004     (.common:Data_Roll)
                  202003c4    00000004     (.common:Data_Yaw)
                  202003c8    00000004     (.common:sensor_timestamp)
                  202003cc    00000002     (.common:sensors)
                  202003ce    00000001     (.common:more)

.data      0    202003d0    00000133     UNINITIALIZED
                  202003d0    00000048     Motor.o (.data.Motor_Left)
                  20200418    00000048     Motor.o (.data.Motor_Right)
                  20200460    0000002c     inv_mpu.o (.data.st)
                  2020048c    00000010     Task_App.o (.data.Gray_Anolog)
                  2020049c    00000010     Task_App.o (.data.Gray_Normal)
                  202004ac    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  202004bc    0000000e     MPU6050.o (.data.hal)
                  202004ca    00000009     MPU6050.o (.data.gyro_orientation)
                  202004d3    00000008     Task_App.o (.data.Data_Tracker_Input)
                  202004db    00000001     Task_App.o (.data.Flag_LED)
                  202004dc    00000008     Task_App.o (.data.Motor)
                  202004e4    00000004     Task_App.o (.data.Data_MotorEncoder)
                  202004e8    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  202004ec    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202004f0    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004f4    00000004     SysTick.o (.data.delayTick)
                  202004f8    00000004     SysTick.o (.data.uwTick)
                  202004fc    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  202004fe    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  202004ff    00000001     Task_App.o (.data.Gray_Digtal)
                  20200500    00000001     Task_App.o (.data.Task_Key.Key_Old)
                  20200501    00000001     Task.o (.data.Task_Num)
                  20200502    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                           code    ro data   rw data
       ------                           ----    -------   -------
    .\
       ti_msp_dl_config.o               3326    121       0      
       startup_mspm0g350x_ticlang.o     8       192       0      
       main.o                           32      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3366    313       0      
                                                                 
    .\APP\Src\
       Task_App.o                       1260    88        241    
       Interrupt.o                      170     0         2      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1430    88        243    
                                                                 
    .\BSP\Src\
       MPU6050.o                        2468    0         70     
       No_Mcu_Ganv_Grayscale_Sensor.o   1244    0         0      
       Serial.o                         404     0         512    
       Task.o                           674     0         241    
       Motor.o                          708     0         144    
       PID_IQMath.o                     402     0         0      
       ADC.o                            236     0         0      
       Key_Led.o                        118     0         0      
       SysTick.o                        106     0         8      
    +--+--------------------------------+-------+---------+---------+
       Total:                           6360    0         975    
                                                                 
    .\DMP\
       inv_mpu_dmp_motion_driver.o      3110    3062      16     
       inv_mpu.o                        4600    82        44     
    +--+--------------------------------+-------+---------+---------+
       Total:                           7710    3144      60     
                                                                 
    D:/CCS/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o     388     0         0      
       dl_timer.o                       356     0         0      
       dl_i2c.o                         192     0         0      
       dl_uart.o                        90      0         0      
       dl_dma.o                         76      0         0      
       dl_adc12.o                       64      0         0      
       dl_common.o                      10      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           1176    0         0      
                                                                 
    D:/CCS/mspm0_sdk_2_05_01_00/source/ti/iqmath/lib/ticlang/m0p/mathacl/mspm0g1x0x_g3x0x/iqmath.a
       _IQNtoF.o                        48      0         0      
       _IQNdiv.o                        24      0         0      
       _IQNmpy.o                        24      0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           96      0         0      
                                                                 
    D:\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                   4510    34        0      
       e_asin.c.obj                     908     0         0      
       s_atan.c.obj                     784     64        0      
       e_atan2.c.obj                    392     0         0      
       e_sqrt.c.obj                     368     0         0      
       qsort.c.obj                      308     0         0      
       aeabi_ctype.S.obj                0       257       0      
       s_scalbn.c.obj                   216     0         0      
       vsnprintf.c.obj                  136     0         0      
       copy_decompress_lzss.c.obj       124     0         0      
       s_frexp.c.obj                    92      0         0      
       _ltoa.c.obj                      88      0         0      
       atoi.c.obj                       64      0         0      
       autoinit.c.obj                   60      0         0      
       boot_cortex_m.c.obj              56      0         0      
       memccpy.c.obj                    34      0         0      
       memcmp.c.obj                     32      0         0      
       copy_zero_init.c.obj             22      0         0      
       copy_decompress_none.c.obj       18      0         0      
       memcpy16.S.obj                   18      0         0      
       wcslen.c.obj                     16      0         0      
       memset16.S.obj                   14      0         0      
       aeabi_portable.c.obj             8       0         4      
       exit.c.obj                       6       0         0      
       pre_init.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           8278    355       4      
                                                                 
    D:\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                   4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           4       0         0      
                                                                 
    D:\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                     434     0         0      
       divdf3.S.obj                     284     0         0      
       muldf3.S.obj                     244     0         0      
       comparedf2.c.obj                 220     0         0      
       addsf3.S.obj                     216     0         0      
       udivmoddi4.S.obj                 162     0         0      
       mulsf3.S.obj                     140     0         0      
       divsf3.S.obj                     130     0         0      
       comparesf2.S.obj                 118     0         0      
       truncdfsf2.S.obj                 116     0         0      
       aeabi_dcmp.S.obj                 98      0         0      
       aeabi_fcmp.S.obj                 98      0         0      
       aeabi_idivmod.S.obj              86      0         0      
       fixdfsi.S.obj                    74      0         0      
       fixunsdfsi.S.obj                 66      0         0      
       aeabi_uidivmod.S.obj             64      0         0      
       extendsfdf2.S.obj                64      0         0      
       floatsisf.S.obj                  60      0         0      
       muldsi3.S.obj                    58      0         0      
       fixsfsi.S.obj                    56      0         0      
       floatsidf.S.obj                  44      0         0      
       floatunsisf.S.obj                40      0         0      
       floatunsidf.S.obj                36      0         0      
       muldi3.S.obj                     36      0         0      
       ashldi3.S.obj                    30      0         0      
       aeabi_uldivmod.S.obj             20      0         0      
       aeabi_memset.S.obj               14      0         0      
       aeabi_memcpy.S.obj               8       0         0      
       aeabi_div0.c.obj                 4       0         0      
    +--+--------------------------------+-------+---------+---------+
       Total:                           3020    0         0      
                                                                 
       Stack:                           0       0         512    
       Linker Generated:                0       116       0      
    +--+--------------------------------+-------+---------+---------+
       Grand Total:                     31440   4016      1794   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00008aa4 records: 2, size/record: 8, table size: 16
	.data: load addr=00008a40, load size=00000050 bytes, run addr=202003d0, run size=00000133 bytes, compression=lzss
	.bss: load addr=00008a9c, load size=00000008 bytes, run addr=20200000, run size=000003cf bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00008a90 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   000024fd     00007ae8     00007ae6   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00003f3d     00007b04     00007b00   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00007b1c          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00007b30          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00007b5c          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00007b94          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00003859     00007b3c     00007b3a   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   00002507     00007b80     00007b7c   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00007ba2          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00006fc9     00007ba8     00007ba4   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
0000558d  ADC0_IRQHandler                      
0000558d  ADC1_IRQHandler                      
0000558d  AES_IRQHandler                       
00007b9c  C$$EXIT                              
0000558d  CANFD0_IRQHandler                    
0000558d  DAC0_IRQHandler                      
000065a1  DL_ADC12_setClockConfig              
00007ad5  DL_Common_delayCycles                
000060e1  DL_DMA_initChannel                   
00005c79  DL_I2C_fillControllerTXFIFO          
00006855  DL_I2C_flushControllerTXFIFO         
00007063  DL_I2C_setClockConfig                
000041e5  DL_SYSCTL_configSYSPLL               
0000596d  DL_SYSCTL_setHFCLKSourceHFXTParams   
000063c5  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003a6d  DL_Timer_initFourCCPWMMode           
00007381  DL_Timer_setCaptCompUpdateMethod     
000076d9  DL_Timer_setCaptureCompareOutCtl     
00007a45  DL_Timer_setCaptureCompareValue      
0000739d  DL_Timer_setClockConfig              
0000625d  DL_UART_init                         
000079ed  DL_UART_setClockConfig               
0000558d  DMA_IRQHandler                       
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
202004e4  Data_MotorEncoder                    
202004e8  Data_Motor_TarSpeed                  
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202004d3  Data_Tracker_Input                   
202004ec  Data_Tracker_Offset                  
202003c4  Data_Yaw                             
0000558d  Default_Handler                      
00007159  Delay                                
202004db  Flag_LED                             
202004fe  Flag_MPU6050_Ready                   
0000558d  GROUP0_IRQHandler                    
0000268f  GROUP1_IRQHandler                    
000042c1  Get_Analog_value                     
000068cd  Get_Anolog_Value                     
00007a75  Get_Digtal_For_User                  
00006a33  Get_Normalize_For_User               
202002f0  GraySensor                           
2020048c  Gray_Anolog                          
202004ff  Gray_Digtal                          
2020049c  Gray_Normal                          
00007b9d  HOSTexit                             
0000558d  HardFault_Handler                    
0000558d  I2C0_IRQHandler                      
0000558d  I2C1_IRQHandler                      
000065e1  Interrupt_Init                       
00005a99  Key_Read                             
00002c89  MPU6050_Init                         
202004dc  Motor                                
0000541d  Motor_GetSpeed                       
202003d0  Motor_Left                           
20200418  Motor_Right                          
00004df5  Motor_SetDuty                        
000056ed  Motor_Start                          
0000575d  MyPrintf_DMA                         
0000558d  NMI_Handler                          
00002691  No_MCU_Ganv_Sensor_Init              
00005679  No_MCU_Ganv_Sensor_Init_Frist        
00006519  No_Mcu_Ganv_Sensor_Task_Without_tick 
00006de5  PID_IQ_Init                          
000034f1  PID_IQ_Prosc                         
00006409  PID_IQ_SetParams                     
0000558d  PendSV_Handler                       
0000558d  RTC_IRQHandler                       
0000159d  Read_Quad                            
00007ba5  Reset_Handler                        
0000558d  SPI0_IRQHandler                      
0000558d  SPI1_IRQHandler                      
0000558d  SVC_Handler                          
00006179  SYSCFG_DL_ADC1_init                  
00006c6d  SYSCFG_DL_DMA_CH_RX_init             
00007799  SYSCFG_DL_DMA_CH_TX_init             
00007abd  SYSCFG_DL_DMA_init                   
00001fe5  SYSCFG_DL_GPIO_init                  
00005ded  SYSCFG_DL_I2C_MPU6050_init           
00005061  SYSCFG_DL_Motor_PWM_init             
00005cd9  SYSCFG_DL_SYSCTL_init                
00007a55  SYSCFG_DL_SYSTICK_init               
00005291  SYSCFG_DL_UART0_init                 
00006f51  SYSCFG_DL_init                       
000050ed  SYSCFG_DL_initPower                  
00005e45  Serial_Init                          
20200000  Serial_RxData                        
00007893  SysGetTick                           
00007b5f  SysTick_Handler                      
00006f79  SysTick_Increasment                  
00007ac9  Sys_GetTick                          
0000558d  TIMA0_IRQHandler                     
0000558d  TIMA1_IRQHandler                     
0000558d  TIMG0_IRQHandler                     
0000558d  TIMG12_IRQHandler                    
0000558d  TIMG6_IRQHandler                     
0000558d  TIMG7_IRQHandler                     
0000558d  TIMG8_IRQHandler                     
000079ff  TI_memcpy_small                      
00007aaf  TI_memset_small                      
000049ed  Task_Add                             
00006621  Task_GraySensor                      
00005af9  Task_IdleFunction                    
00004105  Task_Init                            
0000644d  Task_Key                             
00006a6d  Task_LED                             
00003d61  Task_Motor_PID                       
00004aa1  Task_Serial                          
000021ad  Task_Start                           
000032a1  Task_Tracker                         
0000558d  UART0_IRQHandler                     
0000558d  UART1_IRQHandler                     
0000558d  UART2_IRQHandler                     
0000558d  UART3_IRQHandler                     
000077b1  _IQ24div                             
000077c9  _IQ24mpy                             
00006c9d  _IQ24toF                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00008aa4  __TI_CINIT_Base                      
00008ab4  __TI_CINIT_Limit                     
00008ab4  __TI_CINIT_Warm                      
00008a90  __TI_Handler_Table_Base              
00008a9c  __TI_Handler_Table_Limit             
00006981  __TI_auto_init_nobinit_nopinit       
0000549d  __TI_decompress_lzss                 
00007a11  __TI_decompress_none                 
00005e9d  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
000078a9  __TI_zero_init_nomemset              
00002507  __adddf3                             
0000447f  __addsf3                             
000087c0  __aeabi_ctype_table_                 
000087c0  __aeabi_ctype_table_C                
00005591  __aeabi_d2f                          
00006211  __aeabi_d2iz                         
0000655d  __aeabi_d2uiz                        
00002507  __aeabi_dadd                         
000059d1  __aeabi_dcmpeq                       
00005a0d  __aeabi_dcmpge                       
00005a21  __aeabi_dcmpgt                       
000059f9  __aeabi_dcmple                       
000059e5  __aeabi_dcmplt                       
00003859  __aeabi_ddiv                         
00003f3d  __aeabi_dmul                         
000024fd  __aeabi_dsub                         
202004f0  __aeabi_errno                        
00007b69  __aeabi_errno_addr                   
000066a1  __aeabi_f2d                          
00006aa5  __aeabi_f2iz                         
0000447f  __aeabi_fadd                         
00005a35  __aeabi_fcmpeq                       
00005a71  __aeabi_fcmpge                       
00005a85  __aeabi_fcmpgt                       
00005a5d  __aeabi_fcmple                       
00005a49  __aeabi_fcmplt                       
00005399  __aeabi_fdiv                         
00005179  __aeabi_fmul                         
00004475  __aeabi_fsub                         
00006db9  __aeabi_i2d                          
00006909  __aeabi_i2f                          
00005f4d  __aeabi_idiv                         
00004d4f  __aeabi_idiv0                        
00005f4d  __aeabi_idivmod                      
00004df3  __aeabi_ldiv0                        
000071d9  __aeabi_llsl                         
000070d1  __aeabi_lmul                         
00007b71  __aeabi_memcpy                       
00007b71  __aeabi_memcpy4                      
00007b71  __aeabi_memcpy8                      
00007a85  __aeabi_memset                       
00007a85  __aeabi_memset4                      
00007a85  __aeabi_memset8                      
000070ad  __aeabi_ui2d                         
00006fa1  __aeabi_ui2f                         
00006661  __aeabi_uidiv                        
00006661  __aeabi_uidivmod                     
0000799d  __aeabi_uldivmod                     
000071d9  __ashldi3                            
ffffffff  __binit__                            
00005839  __cmpdf2                             
000069bd  __cmpsf2                             
00003859  __divdf3                             
00005399  __divsf3                             
00005839  __eqdf2                              
000069bd  __eqsf2                              
000066a1  __extendsfdf2                        
00006211  __fixdfsi                            
00006aa5  __fixsfsi                            
0000655d  __fixunsdfsi                         
00006db9  __floatsidf                          
00006909  __floatsisf                          
000070ad  __floatunsidf                        
00006fa1  __floatunsisf                        
00005519  __gedf2                              
00006945  __gesf2                              
00005519  __gtdf2                              
00006945  __gtsf2                              
00005839  __ledf2                              
000069bd  __lesf2                              
00005839  __ltdf2                              
000069bd  __ltsf2                              
UNDEFED   __mpu_init                           
00003f3d  __muldf3                             
000070d1  __muldi3                             
000069f9  __muldsi3                            
00005179  __mulsf3                             
00005839  __nedf2                              
000069bd  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000024fd  __subdf3                             
00004475  __subsf3                             
00005591  __truncdfsf2                         
00004d51  __udivmoddi4                         
00006fc9  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00007bb9  _system_pre_init                     
00007b97  abort                                
000062a5  adc_getValue                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00002819  atan2                                
00002819  atan2l                               
00000df5  atanl                                
000066e1  atoi                                 
ffffffff  binit                                
000057cd  convertAnalogToDigital               
202004f4  delayTick                            
000062ed  dmp_enable_6x_lp_quat                
000010ed  dmp_enable_feature                   
00005b59  dmp_enable_gyro_cal                  
00006335  dmp_enable_lp_quat                   
000073d5  dmp_load_motion_driver_firmware      
00001c15  dmp_read_fifo                        
000079b1  dmp_register_android_orient_cb       
000079c5  dmp_register_tap_cb                  
00004f31  dmp_set_fifo_rate                    
000029a1  dmp_set_orientation                  
00006491  dmp_set_shake_reject_thresh          
00006b79  dmp_set_shake_reject_time            
00006bab  dmp_set_shake_reject_timeout         
00005907  dmp_set_tap_axes                     
000064d5  dmp_set_tap_count                    
00001365  dmp_set_tap_thresh                   
00006d2d  dmp_set_tap_time                     
00006d5d  dmp_set_tap_time_multi               
20200502  enable_group1_irq                    
00005d35  frexp                                
00005d35  frexpl                               
000089fc  hw                                   
00000000  interruptVectors                     
0000439d  ldexp                                
0000439d  ldexpl                               
00007179  main                                 
000070f5  memccpy                              
00007199  memcmp                               
202003ce  more                                 
00005bb9  mpu6050_i2c_sda_unlock               
00004879  mpu_configure_fifo                   
00005605  mpu_get_accel_fsr                    
00005c19  mpu_get_gyro_fsr                     
00006b45  mpu_get_sample_rate                  
000033c9  mpu_init                             
00003615  mpu_load_firmware                    
00003b71  mpu_lp_accel_mode                    
00003965  mpu_read_fifo_stream                 
00004b4d  mpu_read_mem                         
000017c9  mpu_reset_fifo                       
00004021  mpu_set_accel_fsr                    
0000235d  mpu_set_bypass                       
00004935  mpu_set_dmp_state                    
000046f1  mpu_set_gyro_fsr                     
00004e95  mpu_set_int_latched                  
00004621  mpu_set_lpf                          
00003e51  mpu_set_sample_rate                  
00003171  mpu_set_sensors                      
00004bf9  mpu_write_mem                        
00002f09  mspm0_i2c_read                       
000047b5  mspm0_i2c_write                      
00004ca5  normalizeAnalogValues                
0000303d  qsort                                
202003a0  quat                                 
00008977  reg                                  
0000439d  scalbn                               
0000439d  scalbnl                              
202003c8  sensor_timestamp                     
202003cc  sensors                              
00002b19  sqrt                                 
00002b19  sqrtl                                
00008930  test                                 
202004f8  uwTick                               
00006721  vsnprintf                            
00007a65  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  dmp_enable_feature                   
00001365  dmp_set_tap_thresh                   
0000159d  Read_Quad                            
000017c9  mpu_reset_fifo                       
00001c15  dmp_read_fifo                        
00001fe5  SYSCFG_DL_GPIO_init                  
000021ad  Task_Start                           
0000235d  mpu_set_bypass                       
000024fd  __aeabi_dsub                         
000024fd  __subdf3                             
00002507  __adddf3                             
00002507  __aeabi_dadd                         
0000268f  GROUP1_IRQHandler                    
00002691  No_MCU_Ganv_Sensor_Init              
00002819  atan2                                
00002819  atan2l                               
000029a1  dmp_set_orientation                  
00002b19  sqrt                                 
00002b19  sqrtl                                
00002c89  MPU6050_Init                         
00002f09  mspm0_i2c_read                       
0000303d  qsort                                
00003171  mpu_set_sensors                      
000032a1  Task_Tracker                         
000033c9  mpu_init                             
000034f1  PID_IQ_Prosc                         
00003615  mpu_load_firmware                    
00003859  __aeabi_ddiv                         
00003859  __divdf3                             
00003965  mpu_read_fifo_stream                 
00003a6d  DL_Timer_initFourCCPWMMode           
00003b71  mpu_lp_accel_mode                    
00003d61  Task_Motor_PID                       
00003e51  mpu_set_sample_rate                  
00003f3d  __aeabi_dmul                         
00003f3d  __muldf3                             
00004021  mpu_set_accel_fsr                    
00004105  Task_Init                            
000041e5  DL_SYSCTL_configSYSPLL               
000042c1  Get_Analog_value                     
0000439d  ldexp                                
0000439d  ldexpl                               
0000439d  scalbn                               
0000439d  scalbnl                              
00004475  __aeabi_fsub                         
00004475  __subsf3                             
0000447f  __addsf3                             
0000447f  __aeabi_fadd                         
00004621  mpu_set_lpf                          
000046f1  mpu_set_gyro_fsr                     
000047b5  mspm0_i2c_write                      
00004879  mpu_configure_fifo                   
00004935  mpu_set_dmp_state                    
000049ed  Task_Add                             
00004aa1  Task_Serial                          
00004b4d  mpu_read_mem                         
00004bf9  mpu_write_mem                        
00004ca5  normalizeAnalogValues                
00004d4f  __aeabi_idiv0                        
00004d51  __udivmoddi4                         
00004df3  __aeabi_ldiv0                        
00004df5  Motor_SetDuty                        
00004e95  mpu_set_int_latched                  
00004f31  dmp_set_fifo_rate                    
00005061  SYSCFG_DL_Motor_PWM_init             
000050ed  SYSCFG_DL_initPower                  
00005179  __aeabi_fmul                         
00005179  __mulsf3                             
00005291  SYSCFG_DL_UART0_init                 
00005399  __aeabi_fdiv                         
00005399  __divsf3                             
0000541d  Motor_GetSpeed                       
0000549d  __TI_decompress_lzss                 
00005519  __gedf2                              
00005519  __gtdf2                              
0000558d  ADC0_IRQHandler                      
0000558d  ADC1_IRQHandler                      
0000558d  AES_IRQHandler                       
0000558d  CANFD0_IRQHandler                    
0000558d  DAC0_IRQHandler                      
0000558d  DMA_IRQHandler                       
0000558d  Default_Handler                      
0000558d  GROUP0_IRQHandler                    
0000558d  HardFault_Handler                    
0000558d  I2C0_IRQHandler                      
0000558d  I2C1_IRQHandler                      
0000558d  NMI_Handler                          
0000558d  PendSV_Handler                       
0000558d  RTC_IRQHandler                       
0000558d  SPI0_IRQHandler                      
0000558d  SPI1_IRQHandler                      
0000558d  SVC_Handler                          
0000558d  TIMA0_IRQHandler                     
0000558d  TIMA1_IRQHandler                     
0000558d  TIMG0_IRQHandler                     
0000558d  TIMG12_IRQHandler                    
0000558d  TIMG6_IRQHandler                     
0000558d  TIMG7_IRQHandler                     
0000558d  TIMG8_IRQHandler                     
0000558d  UART0_IRQHandler                     
0000558d  UART1_IRQHandler                     
0000558d  UART2_IRQHandler                     
0000558d  UART3_IRQHandler                     
00005591  __aeabi_d2f                          
00005591  __truncdfsf2                         
00005605  mpu_get_accel_fsr                    
00005679  No_MCU_Ganv_Sensor_Init_Frist        
000056ed  Motor_Start                          
0000575d  MyPrintf_DMA                         
000057cd  convertAnalogToDigital               
00005839  __cmpdf2                             
00005839  __eqdf2                              
00005839  __ledf2                              
00005839  __ltdf2                              
00005839  __nedf2                              
00005907  dmp_set_tap_axes                     
0000596d  DL_SYSCTL_setHFCLKSourceHFXTParams   
000059d1  __aeabi_dcmpeq                       
000059e5  __aeabi_dcmplt                       
000059f9  __aeabi_dcmple                       
00005a0d  __aeabi_dcmpge                       
00005a21  __aeabi_dcmpgt                       
00005a35  __aeabi_fcmpeq                       
00005a49  __aeabi_fcmplt                       
00005a5d  __aeabi_fcmple                       
00005a71  __aeabi_fcmpge                       
00005a85  __aeabi_fcmpgt                       
00005a99  Key_Read                             
00005af9  Task_IdleFunction                    
00005b59  dmp_enable_gyro_cal                  
00005bb9  mpu6050_i2c_sda_unlock               
00005c19  mpu_get_gyro_fsr                     
00005c79  DL_I2C_fillControllerTXFIFO          
00005cd9  SYSCFG_DL_SYSCTL_init                
00005d35  frexp                                
00005d35  frexpl                               
00005ded  SYSCFG_DL_I2C_MPU6050_init           
00005e45  Serial_Init                          
00005e9d  __TI_ltoa                            
00005f4d  __aeabi_idiv                         
00005f4d  __aeabi_idivmod                      
000060e1  DL_DMA_initChannel                   
00006179  SYSCFG_DL_ADC1_init                  
00006211  __aeabi_d2iz                         
00006211  __fixdfsi                            
0000625d  DL_UART_init                         
000062a5  adc_getValue                         
000062ed  dmp_enable_6x_lp_quat                
00006335  dmp_enable_lp_quat                   
000063c5  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00006409  PID_IQ_SetParams                     
0000644d  Task_Key                             
00006491  dmp_set_shake_reject_thresh          
000064d5  dmp_set_tap_count                    
00006519  No_Mcu_Ganv_Sensor_Task_Without_tick 
0000655d  __aeabi_d2uiz                        
0000655d  __fixunsdfsi                         
000065a1  DL_ADC12_setClockConfig              
000065e1  Interrupt_Init                       
00006621  Task_GraySensor                      
00006661  __aeabi_uidiv                        
00006661  __aeabi_uidivmod                     
000066a1  __aeabi_f2d                          
000066a1  __extendsfdf2                        
000066e1  atoi                                 
00006721  vsnprintf                            
00006855  DL_I2C_flushControllerTXFIFO         
000068cd  Get_Anolog_Value                     
00006909  __aeabi_i2f                          
00006909  __floatsisf                          
00006945  __gesf2                              
00006945  __gtsf2                              
00006981  __TI_auto_init_nobinit_nopinit       
000069bd  __cmpsf2                             
000069bd  __eqsf2                              
000069bd  __lesf2                              
000069bd  __ltsf2                              
000069bd  __nesf2                              
000069f9  __muldsi3                            
00006a33  Get_Normalize_For_User               
00006a6d  Task_LED                             
00006aa5  __aeabi_f2iz                         
00006aa5  __fixsfsi                            
00006b45  mpu_get_sample_rate                  
00006b79  dmp_set_shake_reject_time            
00006bab  dmp_set_shake_reject_timeout         
00006c6d  SYSCFG_DL_DMA_CH_RX_init             
00006c9d  _IQ24toF                             
00006d2d  dmp_set_tap_time                     
00006d5d  dmp_set_tap_time_multi               
00006db9  __aeabi_i2d                          
00006db9  __floatsidf                          
00006de5  PID_IQ_Init                          
00006f51  SYSCFG_DL_init                       
00006f79  SysTick_Increasment                  
00006fa1  __aeabi_ui2f                         
00006fa1  __floatunsisf                        
00006fc9  _c_int00_noargs                      
00007063  DL_I2C_setClockConfig                
000070ad  __aeabi_ui2d                         
000070ad  __floatunsidf                        
000070d1  __aeabi_lmul                         
000070d1  __muldi3                             
000070f5  memccpy                              
00007159  Delay                                
00007179  main                                 
00007199  memcmp                               
000071d9  __aeabi_llsl                         
000071d9  __ashldi3                            
00007381  DL_Timer_setCaptCompUpdateMethod     
0000739d  DL_Timer_setClockConfig              
000073d5  dmp_load_motion_driver_firmware      
000076d9  DL_Timer_setCaptureCompareOutCtl     
00007799  SYSCFG_DL_DMA_CH_TX_init             
000077b1  _IQ24div                             
000077c9  _IQ24mpy                             
00007893  SysGetTick                           
000078a9  __TI_zero_init_nomemset              
0000799d  __aeabi_uldivmod                     
000079b1  dmp_register_android_orient_cb       
000079c5  dmp_register_tap_cb                  
000079ed  DL_UART_setClockConfig               
000079ff  TI_memcpy_small                      
00007a11  __TI_decompress_none                 
00007a45  DL_Timer_setCaptureCompareValue      
00007a55  SYSCFG_DL_SYSTICK_init               
00007a65  wcslen                               
00007a75  Get_Digtal_For_User                  
00007a85  __aeabi_memset                       
00007a85  __aeabi_memset4                      
00007a85  __aeabi_memset8                      
00007aaf  TI_memset_small                      
00007abd  SYSCFG_DL_DMA_init                   
00007ac9  Sys_GetTick                          
00007ad5  DL_Common_delayCycles                
00007b5f  SysTick_Handler                      
00007b69  __aeabi_errno_addr                   
00007b71  __aeabi_memcpy                       
00007b71  __aeabi_memcpy4                      
00007b71  __aeabi_memcpy8                      
00007b97  abort                                
00007b9c  C$$EXIT                              
00007b9d  HOSTexit                             
00007ba5  Reset_Handler                        
00007bb9  _system_pre_init                     
000087c0  __aeabi_ctype_table_                 
000087c0  __aeabi_ctype_table_C                
00008930  test                                 
00008977  reg                                  
000089fc  hw                                   
00008a90  __TI_Handler_Table_Base              
00008a9c  __TI_Handler_Table_Limit             
00008aa4  __TI_CINIT_Base                      
00008ab4  __TI_CINIT_Limit                     
00008ab4  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  GraySensor                           
202003a0  quat                                 
202003b0  Data_Accel                           
202003b6  Data_Gyro                            
202003bc  Data_Pitch                           
202003c0  Data_Roll                            
202003c4  Data_Yaw                             
202003c8  sensor_timestamp                     
202003cc  sensors                              
202003ce  more                                 
202003d0  Motor_Left                           
20200418  Motor_Right                          
2020048c  Gray_Anolog                          
2020049c  Gray_Normal                          
202004d3  Data_Tracker_Input                   
202004db  Flag_LED                             
202004dc  Motor                                
202004e4  Data_MotorEncoder                    
202004e8  Data_Motor_TarSpeed                  
202004ec  Data_Tracker_Offset                  
202004f0  __aeabi_errno                        
202004f4  delayTick                            
202004f8  uwTick                               
202004fe  Flag_MPU6050_Ready                   
202004ff  Gray_Digtal                          
20200502  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[312 symbols]
