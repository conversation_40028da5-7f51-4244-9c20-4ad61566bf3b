#include "Serial.h"

uint8_t Serial_RxData[MAX_RX_LENGTH]; //数据接收数组

void Serial_Init(void)
{
    DL_DMA_setSrcAddr(DMA, DMA_CH_RX_CHAN_ID, (uint32_t)(&UART0->RXDATA)); //配置接收DMA的源地址
    DL_DMA_setDestAddr(DMA, DMA_CH_RX_CHAN_ID, (uint32_t)(&Serial_RxData[0])); //配置接收DMA的目的地址
    DL_DMA_setTransferSize(DMA, DMA_CH_RX_CHAN_ID, MAX_RX_LENGTH); //配置DMA数据传输大小
    DL_DMA_enableChannel(DMA, DMA_CH_RX_CHAN_ID); //开启接收DMA

    DL_DMA_setDestAddr(DMA, DMA_CH_TX_CHAN_ID, (uint32_t)(&UART0->TXDATA)); //配置接收DMA的目的地址
    DL_DMA_disableChannel(DMA, DMA_CH_TX_CHAN_ID);
}
/**
 * @brief 阻塞式发送数据
 * 
 * @param format 格式化字符串
 * @param ... 可变参数
 * @return uint16_t 格式化后字符长短
 */
uint16_t MyPrintf(char *format, ...)
{
    DL_DMA_disableChannel(DMA, DMA_CH_TX_CHAN_ID);
    char txbuffer[MAX_TX_LENGTH];
    uint16_t len = 0;
    va_list args;
    va_start(args, format);
    len = vsnprintf(txbuffer, MAX_TX_LENGTH, format, args);
    va_end(args);
    for (uint16_t i = 0; i < len; i++)
    {
        DL_UART_transmitDataBlocking(UART0_INST, txbuffer[i]);
    }
    return len;
}

/**
 * @brief DMA发送数据
 * 
 * @param format 格式化字符串
 * @param ... 可变参数
 * @return uint16_t 格式化后字符长短
 */
uint16_t MyPrintf_DMA(char *format, ...)
{
    char Serial_TxData[MAX_TX_LENGTH];
    uint16_t len = 0;
    va_list args;
    va_start(args, format);
    len = vsnprintf(Serial_TxData, MAX_TX_LENGTH, format, args);
    va_end(args);

    DL_DMA_disableChannel(DMA, DMA_CH_TX_CHAN_ID);
    DL_DMA_setSrcAddr(DMA, DMA_CH_TX_CHAN_ID, (uint32_t)(&Serial_TxData[0])); //配置接收DMA的源地址
    DL_DMA_setTransferSize(DMA, DMA_CH_TX_CHAN_ID, len); //配置DMA数据传输大小
    DL_DMA_enableChannel(DMA, DMA_CH_TX_CHAN_ID);

    return len;
}